import {
  Component,
  Input,
  On<PERSON>hanges,
  On<PERSON>nit,
  OnDestroy,
  SimpleChang<PERSON>,
} from '@angular/core';
import { Appointment } from '../../models/appointment.model';
import { getSessionWaitingTime, Session } from '../../models/session.model';
import { PatientHistoryComponent } from '../patient-history/patient-history.component';
import { MatDialog } from '@angular/material/dialog';
import { User } from '../../models/user.model';
import { StorageService } from '../../../core/services/storage.service';
import { ProfileService } from '../../services/profile.service';
import { ErrorService } from '../../services/error.service';
import { SocketService } from 'src/app/core/services/socket.service';
import { Direction } from '@angular/cdk/bidi';
import {AppointmentService} from '../../services/appointment.service';
import {FileUploadComponent} from '../file-upload/file-upload.component';
import {SessionService} from '../../services/session.service';
import {PatientExtraComponent} from '../patient-extra/patient-extra.component';
import {FormBuilder, FormGroup} from "@angular/forms";
import {debounceTime, distinctUntilChanged} from "rxjs/operators";

@Component({
  selector: 'app-patient-appointment-card',
  templateUrl: './patient-appointment-card.component.html',
  styleUrls: ['./patient-appointment-card.component.scss'],
})
export class PatientAppointmentCardComponent
  implements OnInit, OnChanges, OnDestroy {
  @Input() session: Session | any;
  @Input() appointment: Appointment;
  @Input() appointmentHistory: Appointment[];
  @Input() showDiagnoses: boolean;
  @Input() noHistory: boolean = false;
  @Input() isEditable: boolean;
  @Input() dir: Direction = 'ltr';
  public waitingTime: number = 0;
  public currentUser: User;
  public detailsForm: FormGroup;

  constructor(
    public dialog: MatDialog,
    private storageService: StorageService,
    private profileService: ProfileService,
    private errorService: ErrorService,
    private socketService: SocketService,
    private appointmentService: AppointmentService,
    private sessionService: SessionService,
    private fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.setCurrentUser();
    if (this.session) {
      if (
        this.session &&
        this.session.appointment &&
        this.session.appointment.startTime
      ) {
        this.waitingTime = getSessionWaitingTime(
          this.session.appointment.startTime,
          this.session.startTime
        );
      }
    } else if (this.appointment) {
      this.session = { appointment: this.appointment };
      if (this.appointment.startTime) {
        this.waitingTime = getSessionWaitingTime(
          this.appointment.startTime,
          new Date()
        );
      }
    }
    this.initSocketListner();
    this.initDetailsForm();
  }



  ngOnChanges(changes: SimpleChanges) {
    if (changes?.appointment?.previousValue) {
      if (this.appointment) {
        this.appointment = changes.appointment.currentValue;
      }
      if (this.session.appointment) {
        this.session.appointment = changes.appointment.currentValue;
      }
    }
  }

  initDetailsForm() {
    this.detailsForm = this.fb.group({
      height: [this.session.height || ''],
      weight: [this.session.weight || '']
    });
    this.detailsForm.valueChanges.pipe(
      debounceTime(500),
      distinctUntilChanged()
    ).subscribe((value) => {
      this.handleDetailsChanges();
    });
  }

  handleDetailsChanges(){
    this.updateSession();
  }
  setCurrentUser() {
    this.currentUser = this.storageService.getUser();
  }

  appoitnmentClick(sessionID: string | undefined) {
    const dialogRef = this.dialog.open(PatientHistoryComponent, {
      width: '90%',
      data: {
        sessionID,
        patientID: this.session.patient._id,
      },
      panelClass: 'no-padding-dialog',
    });
    dialogRef.afterClosed().subscribe(() => {});
  }
  removeChip(item: string, arrayName: string): void {
    const index = this.session[arrayName].indexOf(item);
    if (index >= 0) {
      this.session[arrayName].splice(index, 1);
      this.updateSession();
    }
  }
  createChip(arrayName: string): void {
    if (!this.session[arrayName]) {
      this.session[arrayName] = [];
    }
    this.session[arrayName].push('');

    setTimeout(() => {
      const element = document.getElementById(
        arrayName + (this.session[arrayName].length - 1)
      );
      if (element) {
        element.focus();
      }
    }, 0);
  }
  handleKeyDown(event: KeyboardEvent, arrayName: string, index: number) {
    // Allow Enter key for line breaks, but use Ctrl+Enter to move to next chip
    if (event.key === 'Enter') {
      if (event.ctrlKey) {
        event.preventDefault();
        this.nextChip(arrayName, index);
      }
      // Otherwise, allow the default Enter behavior for line breaks
    } else {
      // Stop propagation for other keys to prevent unwanted behavior
      event.stopPropagation();
    }
  }

  nextChip(arrayName: string, index: number) {
    if (this.session[arrayName].length > index + 1) {
      setTimeout(() => {
        const element = document.getElementById(arrayName + (index + 1));
        if (element) {
          element.focus();
        }
      }, 0);
    } else {
      setTimeout(() => {
        const element = document.getElementById(arrayName + index);
        if (element && element.parentElement) {
          element.parentElement.focus();
        }
      }, 0);
    }
  }
  editChip(index: number, value: string, arrayName: string): void {
    if (index >= 0) {
      this.session[arrayName][index] = value;
      this.updateSession();
    }
  }
  savePatient() {
    this.profileService
      .updateProfile(this.session.appointment.patient)
      .subscribe(
        (res) => {},
        (error) => this.errorService.handleError(error)
      );
  }

  updateSession() {

    const details = this.detailsForm.value;
    this.sessionService.updateSession({_id: this.session._id, allergies: this.session.allergies, chronicDiseases: this.session.chronicDiseases, permanentDrugs: this.session.permanentDrugs, ...details} as any).subscribe(
      (res) => {},
      (error) => this.errorService.handleError(error)
    );
  }

  initSocketListner() {
    if (this.session._id) {
      this.socketService.listen(this.session._id).subscribe((res: any) => {
        if (res.session) {
          // this.session = res.session;
        }
      });
    }
    if (this.session.appointment._id) {
      this.socketService
        .listen(this.session.appointment._id)
        .subscribe((res: any) => {
          if (res.appointment) {
            this.session.appointment = res.appointment;
          }
        });
    }
    if (this.session.appointment.patient._id) {
      this.socketService
        .listen(this.session.appointment.patient._id)
        .subscribe((res: any) => {
          if (res.profile) {
            this.session.appointment.patient = res.profile;
          }
        });
    }
  }

  removeFile(file: { title: string; link: string }) {
    const files = this.session.appointment.files?.filter(
      (listFile: { title: string; link: string }) => listFile.link !== file.link
    );
    this.appointmentService.updateAppointment({_id:  this.session.appointment._id, files}).subscribe(
      (res: any) => {
        this.session.appointment.files = files;
      },
      (error: any) => this.errorService.handleError(error)
    );

  }
  openUploadDialog($event: Event) {
    $event.preventDefault();
    const dialogRef = this.dialog.open(FileUploadComponent, {
      width: '800px',
      data: {},
    });
    dialogRef.afterClosed().subscribe((files) => {

      const newFileList = JSON.parse(JSON.stringify(this.session.appointment.files));
      newFileList.push(...files);
      this.appointmentService.updateAppointment({_id:  this.session.appointment._id, files: newFileList}).subscribe(
        (res: any) => {
          this.session.appointment.files = newFileList;
        },
        (error: any) => this.errorService.handleError(error)
      );
    });
  }

  ngOnDestroy(): void {
    this.socketService.removeAllListeners(this.session._id + '');
    this.socketService.removeAllListeners(this.session.appointment._id + '');
    this.socketService.removeAllListeners(
      this.session.appointment.patient._id + ''
    );
  }

  openExtraInfo() {
    const dialogRef = this.dialog.open(PatientExtraComponent, {
      width: '800px',
      data: {
        patient: this.session.patient,
      },
    });

    dialogRef.afterClosed().subscribe(() => {

    });
  }
}
