{"general": {"appointment": "<PERSON><PERSON><PERSON>vous", "appointmentTime": "<PERSON><PERSON><PERSON>vous", "waitingTimes": "Temps d’attente", "waitingTimesShort": "<PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "searchPlaceHolder": "<PERSON><PERSON>, Prénom, ticket", "minuteShort": "min", "minute": "minute", "minutes": "minutes", "type": "Type", "email": "Email", "years": "ans", "days": {"sunday": "<PERSON><PERSON><PERSON>", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>"}, "from": "De", "to": "À", "choseDate": "Choisir une date", "files": "Fichiers", "warningTitle": "Attention", "addButton": "AJOUTER", "modifyButton": "MODIFIER", "selectProfile": "Selectionner un", "patients": "patients", "sex": {"male": "<PERSON><PERSON>", "female": "<PERSON>mme"}, "tooltips": {"add": "Ajouter", "edit": "Modifier", "search": "<PERSON><PERSON><PERSON>"}, "extraInfo": "Informations supplémentaires"}, "login": {"password": "Mot de passe", "logIn": "Connexion", "reset": "Réinitialiser", "emailOrPasswordError": "L'email ou bien le mot de passe est invalide", "emailError": "L'email est invalide", "resetMessage": "Un lien de réinitialisation de mot de passe a été envoyé a votre email", "passwordForgotten": "Mot de passe oublié ?", "returnToLogin": "Retour au login"}, "navbar": {"noNotifications": "Pas de notifications", "myAccount": "Mon compte", "disconnect": "Déconnecter", "averageWaitingTime": "Temps d'attente moyen", "coming": "À venir", "waiting": "Dont attendents ", "done": "Traités", "averageSessionTime": "Temps de session moyen", "tooltips": {"notifications": "Notifications", "settings": "Paramètres"}}, "pages": {"home": "Accueil", "appointments": "<PERSON><PERSON><PERSON>vous", "currentSession": "Session courante", "patients": "Patients", "provisions": "Provisions", "diagnosis": "Diagnostics", "stats": "Statistiques", "configuration": "Configuration", "clients": "Clients", "termsOfUse": "Conditions d'utilisation"}, "profileTypes": {"patient": "patient", "doctor": "docteur", "receptionist": "réceptionniste", "supplier": "<PERSON><PERSON><PERSON><PERSON>"}, "doctorSummary": {"nextAppointment": "<PERSON><PERSON><PERSON>vous suivant", "nextAppointments": "Rendez-vous prochaines", "noAppointmentsInTheWaitingList": "AUCUNE RENDEZ-VOUS TROUVE EST EN ATTENTE", "appointment": "<PERSON><PERSON><PERSON>vous", "ofLateness": "de retard", "noDescriptionFound": "Aucune description pour cette session", "allergies": "allergies", "chronicDiseases": "Maladies chroniques", "permanentDrugs": "Médicaments permanents", "appointmentFiles": "Fichiers de rendez-vous", "noResults": "Aucune resultat", "noSessionFound": "AUCUNE SESSION N'A ÉTÉ AFFECTÉE", "peopleWaiting": "Personnes en attente", "averageWaitingTime": "Temps d'attente moyen", "validate": "Valider", "futureAppointments": "Prochains rendez-vous", "noAppointmentsItemsInTheWaitingList": "AUCUN RENDEZ-VOUS EN ATTENTE", "goToAppointments": "aller au rendez-vous", "breaksAndUnavailability": "Pauses et indisponibilité", "sessionsHistory": "HISTORIQUE DES CONSULTATIONS", "viewMore": "Voir plus", "tooltips": {"addBreak": "Ajouter une pause"}}, "doctorSelection": {"doctor": "M<PERSON><PERSON><PERSON><PERSON>", "selectAll": "<PERSON><PERSON>"}, "breaks": {"atClinic": "Au clinique", "atPatient": "Chez patient", "break": "Pause", "dailyBreak": "<PERSON><PERSON> de dé<PERSON>", "start": "Debut", "end": "Fin", "timeLeft": "Temps restant", "status": "Status", "disabled": "Désactivé", "alreadyPassed": "Déja passé"}, "appointments": {"search": "<PERSON><PERSON><PERSON>", "passedSessions": "Sessions traitées", "date": "Date", "from": "De", "to": "à", "noBreaksFound": "Aucun indisponibilité n'est trouvée", "noAppointmentsInHold": "AUCUN RENDEZ-VOUS EN COURS", "noAppointmentsFound": "AUCUN RENDEZ-VOUS EN ATTENTE", "states": {"inProgress": "En cours", "waiting": "En attente", "canceled": "<PERSON><PERSON><PERSON>", "passed": "Traitée", "almostCompleted": "Vient de finir"}, "options": {"edit": "Modifier le rendez-vous", "makeFutureAppointment": "Programmer un rendez-vous futur", "sendMessage": "Envoyer un message", "downloadInvoice": "Télécharger la facture", "downloadPrescription": "Télécharger l'ordonnance", "printInvoice": "Imprimer la facture", "printPrescription": "Imprimer l'ordonnance"}, "appointmentsConflict": "il y a un conflit entre rendez-vous", "dialog": {"propositions": "Propositions", "fullDay": "<PERSON><PERSON><PERSON> pleine", "noDoctorSelectedInfo": "Sélectionner un médecin pour obtenir les disponibilités", "visitType": "Type de visite", "comment": "Commentaire", "commentPlaceHolder": "Ex. le rendez-vous ...", "requiredDocuments": "Documents à fournir lors du rendez-vous", "requiredDocumentsPlaceHolder": "Nouveau document..."}, "daily": "Quotidienne", "weekly": "<PERSON><PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "tooltips": {"addAppointment": "Ajouter un rendez-vous", "breakList": "Liste des pauses", "sendMessage": "Envoyer un message", "editAppointment": "Modifier le rendez-vous", "uploadFile": "Ajouter un fichier", "downloadFile": "Télécharger le fichier", "deleteFile": "<PERSON><PERSON><PERSON><PERSON> le fichier"}}, "currentSession": {"noAffectedSession": "AUCUNE SESSION N'A ÉTÉ AFFECTÉE", "endSession": "Valider la Consultation", "scheduleFutureAppointment": "Programmer un futur rendez-vous", "notes": "NOTES", "note": "NOTE", "prescription": "ORDONNANCE", "diagnoses": "DIAGNOSTICS", "diagnose": "Diagnostic", "notesList": {"addA": "Ajouter une", "withoutTitle": "sans titre", "note": "note", "noteTitle": "Titre de note", "descriptionDefault": "Note"}, "prescriptionList": {"addA": "Ajouter un", "withoutTitle": "sans titre", "drug": "médicament", "usage": "utilisation", "prescriptionTitle": "Titre de médicament", "descriptionDefault": "Conseil d'utilisation"}, "prescriptions": {"prescriptions": "Prescriptions", "ordonnance": "ORDONNANCE", "radiologie": "RADIOLOGIE", "biologie": "BIOLOGIE", "autre": "AUTRE"}, "tooltips": {"addAllergie": "Ajouter une allergie", "addChronicDisease": "Ajouter une maladie chronique", "addPermanentDrug": "Ajouter un médicament permanent", "uploadFile": "Ajouter un fichier", "downloadFile": "Télécharger le fichier", "deleteFile": "<PERSON><PERSON><PERSON><PERSON> le fichier", "add": "Ajouter"}}, "patients": {"noPatientsFound": "AUCUN PATIENT TROUVÉ", "appointmentCount": "Total des rendez-vous", "phoneNumber": "Numéro de téléphone", "appointments": "rendez-vous", "tooltips": {"addPatient": "Ajouter un patient", "cunsultSessionHistory": "Consulter l'historique des consultations", "sendMessage": "Envoyer un message", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}}, "supplies": {"noSuppliesFound": "AUCUN PROVISION TROUVÉ", "quantityAvailable": "Quantité en stock", "item": "article", "usedItem": "consommation", "averageTime": "<PERSON><PERSON><PERSON> moyenne", "consumption": "Consommations", "sellingPrice": "Prix de vente", "itemType": "Nature d'article", "types": {"material": "<PERSON><PERSON><PERSON>", "drug": "Médicament", "session": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>", "radiologie": "Radiologie", "biologie": "Biologie"}, "dialog": {"anItem": "UN ARTICLE", "name": "Nom", "namePlaceholder": "Article", "type": "Type", "quantity": "Quantité", "buyingPrice": "Prix coûtant", "sellingPrice": "Prix de vente", "description": "Description", "descriptionPlaceholder": "Cette maladie est.."}, "tooltips": {"addSupply": "Ajouter un article", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}}, "diagnoses": {"noDiagnosesFound": "AUCUN DIAGNOSTIC TROUVÉ", "specialty": "Specialité", "visitsCount": "Nombre de visites", "visit": "visite", "patientsCount": "Nombre de patients", "patient": "patient", "severity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dangerTypes": {"severe": "Grave", "moderate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minor": "Mineure", "unknown": "Inconnue"}, "dialog": {"aDiagnose": "UN DIAGNOSE", "name": "Nom", "namePlaceholder": "Diagnosis", "specialty": "Specialité", "description": "Description", "descriptionPlaceholder": "Cette maladie est..", "severity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contagious": "Contag<PERSON><PERSON>"}, "tooltips": {"addDiagnose": "Ajouter un diagnostic", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}}, "stats": {"week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON>", "fromTheBeginning": "Dès le début", "appointmentsCount": "Nombre totale des rendez-vous", "effectivenessRate": "<PERSON><PERSON> d'efficaci<PERSON>", "totalGain": "Bénéfices totale", "averageWaitingTime": "Temps d'attente moyen", "appointmentDistributionByType": "Distribution des rendez-vous par types", "appointmentDistributionByGain": "Distribution des bénéfices par types", "appointmentsAveragesByWeeks": "moyenne de rendez-vous par jours de semaine", "averageWaitingTimesVariation": "Variation de temps d'attente moyen/temps de retard", "evolution": "Evolution du taux d'efficacité,heures d'ouverture et heures d'occupation", "appointmentsCountEvolution": "Evolution de nombre des rendez-vous par types dans le temps", "gainEvolution": "Evolution des bénéfices par types  dans le temps"}, "config": {"office": "Cabinet", "breaks": "Pauses et indisponibilité", "team": "Equipe", "others": "Autre configurations", "generalInfo": {"infoAboutTheOffice": "Information et contact", "officeName": "Nom", "officeType": "Type", "mobile": "Télephone", "phone": "Télephone fixe", "fax": "Fax", "email": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "averageSessionTime": "Durée moyenne des séances", "currency": "<PERSON><PERSON>", "hospitalTypes": {"medicalOffice": "Cabinet Medicale", "dentalOffice": "Cabinet <PERSON>taire", "clinic": "Clinique", "careFacility": "Etablissements de soin", "other": "<PERSON><PERSON>"}, "sessionTypes": "Types des sessions", "sessionType": {"defaultName": "<PERSON><PERSON><PERSON>", "price": "Prix par defaut", "priceShort": "<PERSON><PERSON>", "averageTime": "<PERSON><PERSON><PERSON> moyenne", "averageTimeShort": "<PERSON><PERSON>"}, "save": "<PERSON><PERSON><PERSON><PERSON>", "schedule": "<PERSON><PERSON><PERSON>", "defaultSchedule": "<PERSON><PERSON> <PERSON><PERSON>", "workTime": "Heures de travail", "breakTime": "<PERSON><PERSON> de pauses", "to": "À", "specialDays": "JOURS SPECIALS"}, "fixBreaks": {"break": "PAUSE", "noBreaksFound": "AUCUN PAUSE OU INDISPONIBILITÉ N'EST DECLARÉ"}, "tooltips": {"addSessionType": "Ajouter un type de séance", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}}, "timeOffsDialogue": {"choseDate": "Choisir une date", "choseWeekDay": "<PERSON><PERSON> un jour de semaine", "conflict": "il y a un conflit avec les rendez-vous", "comment": "Commentaire"}, "search": {"searchProfile": "RECHERCHER UN PROFILE", "searchListOf": "Rechercher la liste des", "noResults": "AUCUN PROFILE TROUVÉ"}, "profileDialog": {"lastName": "Nom", "firstName": "Prénom", "profileType": "Type de profile", "sex": "<PERSON>e", "chooseSex": "choisissez le sexe", "phoneNumber": "Numéro de téléphone", "email": "Email", "cardID": "CIN", "birthDate": "Date de naissance", "insurance": "Assurance", "insuranceID": "Idantifiant d'assurance", "address": "Addresse", "showDetails": "Informations supplémentaires", "isAdmin": "Est Manager ?", "professionalInformation": "Informations professionnelles", "identifier": "Identifiant", "height": "<PERSON><PERSON>", "weight": "Poids", "title": "Titre", "chooseTitle": "choisissez la profession", "titleOptions": {"admin": "Administrateur", "doctor": "<PERSON><PERSON><PERSON>", "nurse": "<PERSON><PERSON>rm<PERSON>"}, "position": "Position", "choosePosition": "choisissez la position", "chooseProfession": "choisissez la profession d'abord", "residency": "Résidence", "chooseResidency": "choisissez la résidence", "seniority": "Ancienneté", "chooseSeniority": "choisissez la seniorité", "nursePositionOptions": {"ibode": "IBODE", "ide": "IDE", "iade": "IADE"}, "DoctorPositionOptions": {"surgeon": "<PERSON><PERSON><PERSON><PERSON>", "physician": "Médecin", "anesthetist": "Anesthésiste"}, "residencyOptions": {"titular": "Titulaire", "temporary": "<PERSON><PERSON><PERSON>"}, "seniorityOptions": {"senior": "Senior", "junior": "Junior", "intern": "Intern"}}, "account": {"profile": {"title": "Profile", "description": "Informations génerales du profile", "editProfile": "Modifier les infos de profile", "editProfileDescriptionP1": "remplissez le formulaire et appuyez sur", "editProfileDescriptionP2": "pour enregistrer les modifications", "validateButton": "Valider", "addProfile": "Ajouter un profile", "addButton": "Ajouter", "editButton": "Modifer", "personalInformation": "Informations personnels"}, "security": {"title": "Sécurité du compte", "description": "Sécurité et connexion", "changePassword": "Modifier votre mot de passe", "editSecurityDescriptionP1": "remplissez le formulaire et appuyez sur", "editSecurityDescriptionP2": "pour enregistrer les modifications", "validateButton": "Valider", "oldPassword": "Entrez votre ancien mot de passe", "oldPasswordError": "<PERSON><PERSON> de<PERSON> entrer le mot de passe actuel", "newPassword": "Entrez votre nouveau mot de passe", "newPasswordError": "V<PERSON> devez entrer un nouveau mot de passe", "newPasswordConfirmation": "<PERSON><PERSON> <PERSON> entrer la confirmation du mot de passe", "newPasswordConfirmationError": "<PERSON><PERSON> de<PERSON> confirmer le nouveau mot de passe", "passwordsNotTheSame": "les mots de passes ne sont pas identiques"}}, "invoice": {"invoice": "Facture", "patient": "PATIENT", "adress": "Addresse", "email": "EMAIL", "mr": "Mr.", "type": "type", "doctor": "<PERSON><PERSON><PERSON>", "date": "Date", "name": "Désignation", "price": "PRIX", "quantity": "QUANTITY", "total": "TOTAL", "finalTotal": "Total", "downloadPdf": "Telecharger PDF", "endSession": "<PERSON><PERSON><PERSON>", "endSession2": "clô<PERSON><PERSON> le rendez-vous", "details": "dé<PERSON>"}, "prescription": {"prescription": "ORDONNANCE", "patient": "PATIENT", "adress": "Addresse", "email": "EMAIL", "doctor": "<PERSON><PERSON><PERSON>", "date": "DATE", "drugs": "MÉDICAMENTS"}, "currency": {"mad": {"mad": "<PERSON><PERSON><PERSON> maro<PERSON>in", "madShort": "DH"}, "sad": {"sad": "<PERSON><PERSON><PERSON>", "sadShort": "SAR"}, "egp": {"egp": "Livre égyptienne", "egpShort": "EGP"}}, "clients": {"code": "Code", "name": "Nom", "responsible": "Responsable", "ville": "Ville", "subscriptionPlan": "Pack", "status": "Statut", "active": "Actif", "tabs": {"general": "Général", "responsible": "Responsable", "invoices": "Factures"}, "save": "<PERSON><PERSON><PERSON><PERSON>", "general": {"editTenant": "Modification tenant", "name": "Nom d'hôpital", "nameError": "Le nom d'hôpital est requis.", "address": "Addresse", "addressError": "L'adresse est requise.", "address2": "Addresse 2", "address3": "Addresse 3", "email": "Email", "emailError": "L'email est requis.", "emailError2": "<PERSON><PERSON> invalide.", "subscriptionPlan": "Plan d'abonnement", "subscriptionPlanError": "Le plan d'abonnement est requis.", "city": "Ville", "cityError": "La ville est requise.", "country": "Pays", "countryError": "Le pays est requis.", "phoneNumber": "Numéro de téléphone 1", "phoneNumberError": "Le numéro de téléphone 1 est requis.", "phoneNumberError2": "Numéro de téléphone invalide.", "phoneNumber2": "Numéro de téléphone 2"}, "responsibleTab": {"firstName": "Prénom", "firstNameError": "Le prénom est requis.", "lastName": "Nom", "lastNameError": "Le nom est requis.", "email": "Email", "emailError": "L'email est requis.", "emailError2": "<PERSON><PERSON> invalide.", "username": "Nom d'utilisateur", "usernameError": "Le nom d'utilisateur est requis.", "address": "Addresse 1", "addressError": "L'adresse est requise.", "address2": "Addresse 2", "address3": "Addresse 3", "city": "Ville", "country": "Pays", "resetPassword": "Réinitialiser le mot de passe"}}, "comingSoon": {"title": "Coming Soon", "description": "Cette page est en cours de construction. Restez à l'écoute pour les mises à jour!"}, "clientsConfig": {"plans": "Packs", "products": "Produits", "specialties": "Spécialités", "yearlyPrice": "Prix annuel", "monthlyPrice": "Prix mensuel", "trimestrielPrice": "Prix trimestriel", "seeMore": "Voir plus"}, "subscriptions": {"noSubscriptionFound": "Il y a un problème avec votre abonnement. Veuillez contacter l'administrateur."}, "products": {"noProductsFound": "Aucune resultat", "tooltips": {"add": "Ajouter un article"}, "dialog": {"namePlaceholder": "Nom", "name": "Nom"}}, "specialties": {"dialog": {"namePlaceholder": "Nom", "name": "Nom"}}, "pagination": {"showing": "Ligne", "page": "Page", "of": "De", "items": "Ligne(s)", "next": "Suivant", "previous": "Précedent", "loadMore": "Afficher Plus"}, "termsOfUse": {"title": "Conditions d'utilisation", "subtitle": "WinMed - Application de gestion médicale", "section1": {"title": "1. Présentation de l'application", "content": "WinMed est une application complète de gestion médicale conçue pour optimiser les opérations des cabinets médicaux, la gestion des dossiers patients et l'intégration CNSS pour les professionnels de santé."}, "section2": {"title": "2. Fonctionnalités et services", "content": "Notre application offre les fonctionnalités clés suivantes :", "feature1": "Gestion complète des dossiers médicaux patients", "feature2": "Système de planification et gestion des rendez-vous", "feature3": "Intégration CNSS pour le traitement des assurances", "feature4": "Facturation médicale et génération de factures"}, "section3": {"title": "3. <PERSON><PERSON><PERSON><PERSON> d'essai gratuite", "content": "WinMed est disponible gratuitement jusqu'au 31 décembre 2025. Après cette date, des frais d'abonnement s'appliqueront selon le plan de service sélectionné."}, "section4": {"title": "4. Services de support", "content": "Le support technique est disponible en tant que service payant. Le support inclut le dépannage, la formation et l'assistance technique. Les frais de support sont séparés de l'abonnement à l'application."}, "section5": {"title": "5. Politique d'inactivité du compte", "content": "Pour les utilisateurs du plan gratuit : Si votre compte reste inactif pendant plus de 2 mois consécutifs, votre compte et toutes les données associées seront définitivement supprimés de nos serveurs."}, "section6": {"title": "6. Responsabilité des données", "content": "Les utilisateurs sont responsables de maintenir des sauvegardes régulières de leurs données. Nous recommandons d'exporter régulièrement les données importantes pour assurer la sécurité et la continuité des données."}, "section7": {"title": "7. Obligations des utilisateurs", "content": "Les utilisateurs doivent se conformer à toutes les lois et réglementations applicables sur la confidentialité médicale lors de l'utilisation de WinMed. L'application doit être utilisée conformément aux normes médicales professionnelles."}, "section8": {"title": "8. Disponibilité du service", "content": "Bien que nous nous efforcions de maintenir 99% de disponibilité, nous ne pouvons garantir un service ininterrompu. La maintenance programmée sera annoncée à l'avance lorsque possible."}, "section9": {"title": "9. <PERSON><PERSON> important", "content": "Veuillez assurer une utilisation régulière de votre compte et la sauvegarde des données pour éviter la perte de données due aux politiques d'inactivité."}, "section10": {"title": "10. Informations de contact", "content": "Pour des questions sur ces conditions ou le support technique, veuillez contacter notre équipe de support via l'application ou visiter notre site web."}, "lastUpdated": "Dernière mise à jour : <PERSON><PERSON> 2025", "contactInfo": "Pour les demandes de support, ve<PERSON><PERSON><PERSON> contacter : <EMAIL>"}}