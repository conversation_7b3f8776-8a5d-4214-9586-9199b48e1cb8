# 🔧 Guide d'Intégration CNSS dans WinMed

## Vue d'ensemble

Ce document détaille comment intégrer chaque API CNSS dans WinMed avec les modifications frontend et backend nécessaires.

---

## 🏗️ Phase 1: Préparation Infrastructure

### 1.1 Configuration Backend

#### Nouvelles Variables d'Environnement
```env
# CNSS Configuration
CNSS_API_BASE_URL=https://api.cnss.ma/
CNSS_CLIENT_ID=winmed_client_id
CNSS_CLIENT_SECRET=winmed_secret_key
CNSS_TIMEOUT=30000
CNSS_RETRY_ATTEMPTS=3
CNSS_SYNC_INTERVAL=86400000
```

#### Installation Dépendances
```bash
npm install axios retry-axios node-cron
```

### 1.2 Structure Fichiers Backend
```
src/
├── config/
│   └── cnss.js                 # Configuration CNSS
├── controllers/
│   ├── CNSSController.js
│   ├── FSEController.js
│   └── CNSSReferentialController.js
├── services/
│   ├── CNSSService.js
│   ├── FSEService.js
│   ├── CNSSAuthService.js
│   └── CNSSReferentialService.js
├── models/
│   ├── FSE.js
│   ├── CNSSReferential.js
│   └── CNSSToken.js
├── routes/
│   ├── cnssRoute.js
│   └── fseRoute.js
├── middlewares/
│   └── cnssAuth.js
└── jobs/
    └── cnssSync.js
```

---

## 🔐 Phase 2: Authentification CNSS

### 2.1 Backend - Service d'Authentification

#### Créer CNSSAuthService.js
```javascript
import axios from 'axios';
import CNSSToken from '../models/CNSSToken';

class CNSSAuthService {
  constructor() {
    this.baseURL = process.env.CNSS_API_BASE_URL;
    this.clientId = process.env.CNSS_CLIENT_ID;
    this.clientSecret = process.env.CNSS_CLIENT_SECRET;
  }

  async authenticateDoctor(inpe, password, staffId) {
    try {
      // FIA1 - Authentification PS
      const psAuthResponse = await axios.post(`${this.baseURL}/auth/ps`, {
        Login: inpe,
        Password: password
      });

      if (psAuthResponse.data.CodeRetour !== 'FP2-001') {
        throw new Error('Authentification CNSS échouée');
      }

      // FIA2 - Exchange Token
      const tokenResponse = await axios.post(`${this.baseURL}/auth/exchange`, {
        Client_id: this.clientId,
        client_secret: this.clientSecret,
        Access_Token: psAuthResponse.data.access_token
      });

      // Sauvegarder token
      const cnssToken = new CNSSToken({
        staff: staffId,
        accessToken: tokenResponse.data.access_token,
        refreshToken: tokenResponse.data.refresh_Token,
        expiresAt: new Date(Date.now() + 3600000) // 1 heure
      });

      await cnssToken.save();
      return cnssToken;
    } catch (error) {
      throw new Error(`Erreur authentification CNSS: ${error.message}`);
    }
  }
}
```

### 2.2 Frontend - Interface Authentification

#### Modifier Login Component
```typescript
// src/app/auth/login/login.component.ts
export class LoginComponent {
  loginForm = this.fb.group({
    email: ['', Validators.required],
    password: ['', Validators.required],
    inpe: [''], // Nouveau champ INPE
    enableCNSS: [false] // Checkbox pour activer CNSS
  });

  async onSubmit() {
    const formData = this.loginForm.value;
    
    try {
      // Connexion WinMed normale
      const loginResponse = await this.authService.login(formData);
      
      // Si CNSS activé et INPE fourni
      if (formData.enableCNSS && formData.inpe) {
        await this.authenticateWithCNSS(formData.inpe, formData.password);
      }
      
      this.router.navigate(['/dashboard']);
    } catch (error) {
      this.handleError(error);
    }
  }

  async authenticateWithCNSS(inpe: string, password: string) {
    try {
      const response = await this.http.post('/api/cnss/auth/login', {
        inpe,
        password,
        doctorId: this.authService.getCurrentUser().id
      }).toPromise();
      
      // Stocker statut CNSS
      localStorage.setItem('cnssEnabled', 'true');
      this.toastr.success('Authentification CNSS réussie');
    } catch (error) {
      this.toastr.warning('Authentification CNSS échouée - Mode hors ligne');
    }
  }
}
```

#### Template Login
```html
<!-- src/app/auth/login/login.component.html -->
<form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
  <!-- Champs existants -->
  <mat-form-field>
    <input matInput placeholder="Email" formControlName="email">
  </mat-form-field>
  
  <mat-form-field>
    <input matInput type="password" placeholder="Mot de passe" formControlName="password">
  </mat-form-field>
  
  <!-- Nouveaux champs CNSS -->
  <mat-checkbox formControlName="enableCNSS">
    Activer l'intégration CNSS
  </mat-checkbox>
  
  <mat-form-field *ngIf="loginForm.get('enableCNSS')?.value">
    <input matInput placeholder="Numéro INPE" formControlName="inpe">
    <mat-hint>Votre numéro d'identification CNSS</mat-hint>
  </mat-form-field>
  
  <button mat-raised-button color="primary" type="submit">
    Se connecter
  </button>
</form>
```

---

## 👤 Phase 3: Gestion Patients CNSS

### 3.1 Backend - Service Patient CNSS

#### Modifier PatientService.js
```javascript
// Ajouter méthodes CNSS
async verifyPatientWithCNSS(patientData) {
  try {
    const cnssResponse = await this.cnssService.verifyPatient({
      NumeroImmatriculataion: patientData.cnssNumber,
      CNIE_CS: patientData.cnieNumber,
      Date_naissance: patientData.birthDate
    });

    if (cnssResponse.CodeRetour === 'FIP1-001') {
      // Mettre à jour patient avec données CNSS
      const updatedPatient = await Patient.findByIdAndUpdate(
        patientData._id,
        {
          cnssEligible: true,
          cnssVerificationDate: new Date(),
          cnssLastSync: new Date()
        },
        { new: true }
      );
      
      return { success: true, patient: updatedPatient, cnssData: cnssResponse };
    }
    
    return { success: false, error: 'Patient non trouvé dans CNSS' };
  } catch (error) {
    return { success: false, error: error.message };
  }
}
```

### 3.2 Frontend - Interface Patient

#### Modifier Patient Form Component
```typescript
// src/app/patients/patient-form/patient-form.component.ts
export class PatientFormComponent {
  patientForm = this.fb.group({
    // Champs existants
    firstName: ['', Validators.required],
    lastName: ['', Validators.required],
    birthDate: ['', Validators.required],
    
    // Nouveaux champs CNSS
    cnssNumber: [''],
    cnieNumber: [''],
    familyRelation: ['ASSURE']
  });

  async verifyCNSSPatient() {
    const formData = this.patientForm.value;
    
    if (!formData.cnssNumber) {
      this.toastr.warning('Numéro d\'immatriculation CNSS requis');
      return;
    }

    try {
      this.loading = true;
      const response = await this.http.post('/api/cnss/patient/verify', {
        numeroImmatriculation: formData.cnssNumber,
        cnie: formData.cnieNumber,
        dateNaissance: formData.birthDate
      }).toPromise();

      if (response.success) {
        // Pré-remplir le formulaire avec données CNSS
        this.patientForm.patchValue({
          firstName: response.cnssData.Prenom,
          lastName: response.cnssData.Nom,
          birthDate: response.cnssData.dateNaissance
        });
        
        this.cnssVerified = true;
        this.toastr.success('Patient vérifié avec CNSS');
      }
    } catch (error) {
      this.toastr.error('Erreur vérification CNSS');
    } finally {
      this.loading = false;
    }
  }
}
```

#### Template Patient Form
```html
<!-- src/app/patients/patient-form/patient-form.component.html -->
<form [formGroup]="patientForm">
  <!-- Section CNSS -->
  <mat-card class="cnss-section">
    <mat-card-header>
      <mat-card-title>Informations CNSS</mat-card-title>
    </mat-card-header>
    
    <mat-card-content>
      <div class="row">
        <mat-form-field class="col-md-6">
          <input matInput placeholder="Numéro d'immatriculation CNSS" 
                 formControlName="cnssNumber">
        </mat-form-field>
        
        <mat-form-field class="col-md-6">
          <input matInput placeholder="Numéro CNIE/Carte séjour" 
                 formControlName="cnieNumber">
        </mat-form-field>
      </div>
      
      <div class="row">
        <mat-form-field class="col-md-6">
          <mat-select placeholder="Lien de parenté" formControlName="familyRelation">
            <mat-option value="ASSURE">Assuré</mat-option>
            <mat-option value="CONJOINT">Conjoint</mat-option>
            <mat-option value="ENFANT">Enfant</mat-option>
            <mat-option value="ASCENDANT">Ascendant</mat-option>
          </mat-select>
        </mat-form-field>
        
        <div class="col-md-6">
          <button mat-raised-button color="accent" type="button" 
                  (click)="verifyCNSSPatient()" [disabled]="loading">
            <mat-icon>verified_user</mat-icon>
            Vérifier avec CNSS
          </button>
        </div>
      </div>
      
      <div *ngIf="cnssVerified" class="cnss-status success">
        <mat-icon>check_circle</mat-icon>
        Patient vérifié avec CNSS
      </div>
    </mat-card-content>
  </mat-card>
  
  <!-- Champs existants -->
  <mat-card class="patient-info">
    <!-- ... reste du formulaire ... -->
  </mat-card>
</form>
```

---

## 📋 Phase 4: Gestion FSE

### 4.1 Backend - Service FSE

#### Créer FSEService.js
```javascript
import FSE from '../models/FSE';
import CNSSService from './CNSSService';

class FSEService extends Service {
  constructor() {
    super(FSE);
    this.cnssService = new CNSSService();
  }

  async createFSEFromSession(sessionId) {
    const session = await Session.findById(sessionId)
      .populate('patient doctor prescriptions diagnoses');
    
    const fse = new FSE({
      hospital: session.hospital,
      session: sessionId,
      patient: session.patient._id,
      doctor: session.doctor._id,
      visitDate: session.date,
      status: 'DRAFT'
    });

    // Convertir prescriptions WinMed vers format CNSS
    await this.convertPrescriptionsToFSE(session.prescriptions, fse);
    
    return await fse.save();
  }

  async verifyFSEWithCNSS(fseId, cnssToken) {
    const fse = await FSE.findById(fseId).populate('patient doctor');
    
    const cnssData = this.prepareFSEForCNSS(fse);
    const verification = await this.cnssService.verifyFSE(cnssData, cnssToken);
    
    // Sauvegarder résultat vérification
    fse.cnssVerification = {
      verified: verification.CodeRetour === 'FIP2-001',
      verificationDate: new Date(),
      alerts: verification.Alertes || [],
      errors: verification.errors || []
    };
    
    await fse.save();
    return fse;
  }

  async submitFSEToCNSS(fseId, cnssToken) {
    const fse = await FSE.findById(fseId);
    
    if (!fse.cnssVerification.verified) {
      throw new Error('FSE doit être vérifiée avant soumission');
    }

    const cnssData = this.prepareFSEForCNSS(fse);
    const submission = await this.cnssService.submitFSE(cnssData, cnssToken);
    
    if (submission.CodeRetour === 'FIP3-001') {
      fse.cnssNumber = submission.NumeroFSE;
      fse.status = 'SUBMITTED';
      fse.cnssSubmission = {
        submitted: true,
        submissionDate: new Date(),
        responseCode: submission.CodeRetour,
        responseMessage: submission.MessageRetour
      };
    }
    
    await fse.save();
    return fse;
  }
}
```

### 4.2 Frontend - Interface FSE

#### Créer FSE Component
```typescript
// src/app/fse/fse-management/fse-management.component.ts
export class FSEManagementComponent implements OnInit {
  fse: any = null;
  verificationResult: any = null;
  loading = false;

  constructor(
    private http: HttpClient,
    private route: ActivatedRoute,
    private toastr: ToastrService
  ) {}

  async ngOnInit() {
    const sessionId = this.route.snapshot.params['sessionId'];
    if (sessionId) {
      await this.createFSEFromSession(sessionId);
    }
  }

  async createFSEFromSession(sessionId: string) {
    try {
      this.loading = true;
      const response = await this.http.post('/api/fse/create', {
        sessionId
      }).toPromise();
      
      this.fse = response.fse;
      this.toastr.success('FSE créée');
    } catch (error) {
      this.toastr.error('Erreur création FSE');
    } finally {
      this.loading = false;
    }
  }

  async verifyFSE() {
    if (!this.fse) return;

    try {
      this.loading = true;
      const response = await this.http.post(`/api/fse/verify`, {
        fseId: this.fse._id
      }).toPromise();
      
      this.verificationResult = response.verification;
      
      if (response.verification.isValid) {
        this.toastr.success('FSE vérifiée avec succès');
      } else {
        this.toastr.warning('FSE contient des alertes');
      }
    } catch (error) {
      this.toastr.error('Erreur vérification FSE');
    } finally {
      this.loading = false;
    }
  }

  async submitFSE() {
    if (!this.fse || !this.verificationResult?.isValid) {
      this.toastr.warning('FSE doit être vérifiée avant soumission');
      return;
    }

    try {
      this.loading = true;
      const response = await this.http.post(`/api/fse/submit`, {
        fseId: this.fse._id,
        finalValidation: true
      }).toPromise();
      
      this.fse = response.fse;
      this.toastr.success(`FSE soumise - Numéro: ${response.cnssNumber}`);
    } catch (error) {
      this.toastr.error('Erreur soumission FSE');
    } finally {
      this.loading = false;
    }
  }
}
```

#### Template FSE
```html
<!-- src/app/fse/fse-management/fse-management.component.html -->
<div class="fse-container">
  <mat-card class="fse-header">
    <mat-card-header>
      <mat-card-title>
        Feuille de Soins Électronique
        <span *ngIf="fse?.cnssNumber" class="cnss-number">
          ({{ fse.cnssNumber }})
        </span>
      </mat-card-title>
      <mat-card-subtitle>
        Statut: 
        <span [class]="'status-' + fse?.status?.toLowerCase()">
          {{ fse?.status }}
        </span>
      </mat-card-subtitle>
    </mat-card-header>
    
    <mat-card-actions>
      <button mat-raised-button color="primary" 
              (click)="verifyFSE()" 
              [disabled]="loading || fse?.status === 'SUBMITTED'">
        <mat-icon>fact_check</mat-icon>
        Vérifier FSE
      </button>
      
      <button mat-raised-button color="accent" 
              (click)="submitFSE()" 
              [disabled]="loading || !verificationResult?.isValid || fse?.status === 'SUBMITTED'">
        <mat-icon>send</mat-icon>
        Soumettre à CNSS
      </button>
    </mat-card-actions>
  </mat-card>

  <!-- Résultat vérification -->
  <mat-card *ngIf="verificationResult" class="verification-result">
    <mat-card-header>
      <mat-card-title>Résultat Vérification CNSS</mat-card-title>
    </mat-card-header>
    
    <mat-card-content>
      <div *ngIf="verificationResult.alerts?.length > 0" class="alerts">
        <h4>Alertes:</h4>
        <mat-list>
          <mat-list-item *ngFor="let alert of verificationResult.alerts">
            <mat-icon [class]="'alert-' + alert.type?.toLowerCase()">
              {{ alert.type === 'ERROR' ? 'error' : 'warning' }}
            </mat-icon>
            <span>{{ alert.message }}</span>
          </mat-list-item>
        </mat-list>
      </div>
      
      <div *ngIf="verificationResult.isValid" class="success-message">
        <mat-icon color="primary">check_circle</mat-icon>
        FSE prête pour soumission
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Détails FSE -->
  <mat-card class="fse-details">
    <!-- Contenu FSE détaillé -->
  </mat-card>
</div>
```

---

## 📚 Phase 5: Synchronisation Référentiels

### 5.1 Backend - Job de Synchronisation

#### Créer cnssSync.js
```javascript
import cron from 'node-cron';
import CNSSReferentialService from '../services/CNSSReferentialService';

const cnssReferentialService = new CNSSReferentialService();

// Synchronisation quotidienne à 2h du matin
cron.schedule('0 2 * * *', async () => {
  console.log('Début synchronisation référentiels CNSS');
  
  try {
    await cnssReferentialService.syncAllReferentials();
    console.log('Synchronisation référentiels CNSS terminée');
  } catch (error) {
    console.error('Erreur synchronisation CNSS:', error);
  }
});

export default cron;
```

### 5.2 Frontend - Interface Référentiels

#### Créer Referentials Component
```typescript
// src/app/cnss/referentials/referentials.component.ts
export class ReferentialsComponent implements OnInit {
  referentials = {
    drugs: [],
    medicalDevices: [],
    medicalActs: [],
    biologyActs: [],
    aldAlc: []
  };
  
  lastSyncDate: Date | null = null;
  syncing = false;

  async ngOnInit() {
    await this.loadReferentials();
  }

  async syncReferentials() {
    try {
      this.syncing = true;
      await this.http.get('/api/cnss/referentials/sync').toPromise();
      await this.loadReferentials();
      this.toastr.success('Référentiels synchronisés');
    } catch (error) {
      this.toastr.error('Erreur synchronisation');
    } finally {
      this.syncing = false;
    }
  }

  async loadReferentials() {
    // Charger tous les référentiels
    const [drugs, devices, acts, bio, ald] = await Promise.all([
      this.http.get('/api/cnss/referentials/drugs').toPromise(),
      this.http.get('/api/cnss/referentials/medical-devices').toPromise(),
      this.http.get('/api/cnss/referentials/medical-acts').toPromise(),
      this.http.get('/api/cnss/referentials/biology-acts').toPromise(),
      this.http.get('/api/cnss/referentials/ald-alc').toPromise()
    ]);
    
    this.referentials = { drugs, devices, acts, bio, ald };
  }
}
```

---

## 🎯 Phase 6: Intégration dans l'Interface Existante

### 6.1 Modifications Session Component

#### Ajouter Bouton FSE
```html
<!-- Dans session-detail.component.html -->
<div class="session-actions">
  <!-- Boutons existants -->
  <button mat-raised-button (click)="completeSession()">
    Terminer Session
  </button>
  
  <!-- Nouveau bouton FSE -->
  <button mat-raised-button color="accent" 
          (click)="createFSE()" 
          *ngIf="session.patient?.cnssEligible && session.status === 'COMPLETED'">
    <mat-icon>description</mat-icon>
    Créer FSE CNSS
  </button>
</div>
```

### 6.2 Modifications Dashboard

#### Ajouter Widget CNSS
```html
<!-- Dans dashboard.component.html -->
<div class="dashboard-widgets">
  <!-- Widgets existants -->
  
  <!-- Nouveau widget CNSS -->
  <mat-card class="cnss-widget">
    <mat-card-header>
      <mat-card-title>CNSS Status</mat-card-title>
    </mat-card-header>
    
    <mat-card-content>
      <div class="cnss-stats">
        <div class="stat">
          <span class="label">FSE en attente:</span>
          <span class="value">{{ cnssStats.pendingFSE }}</span>
        </div>
        <div class="stat">
          <span class="label">FSE soumises:</span>
          <span class="value">{{ cnssStats.submittedFSE }}</span>
        </div>
        <div class="stat">
          <span class="label">Compléments:</span>
          <span class="value">{{ cnssStats.pendingComplements }}</span>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
```

---

## ✅ Checklist d'Implémentation

### Backend
- [ ] Créer modèles FSE, CNSSReferential, CNSSToken
- [ ] Modifier modèles existants (Patient, Staff, Hospital, etc.)
- [ ] Créer services CNSS (Auth, FSE, Referential)
- [ ] Créer controllers CNSS
- [ ] Ajouter routes CNSS
- [ ] Configurer job de synchronisation
- [ ] Ajouter middleware authentification CNSS
- [ ] Tests unitaires services CNSS

### Frontend
- [ ] Modifier composant login pour CNSS
- [ ] Modifier formulaire patient pour champs CNSS
- [ ] Créer composant gestion FSE
- [ ] Créer composant référentiels CNSS
- [ ] Modifier dashboard pour widgets CNSS
- [ ] Ajouter notifications CNSS
- [ ] Modifier session pour intégration FSE
- [ ] Tests e2e workflows CNSS

### Configuration
- [ ] Variables d'environnement CNSS
- [ ] Configuration serveur CNSS
- [ ] Scripts de migration base de données
- [ ] Documentation utilisateur
- [ ] Formation équipe
