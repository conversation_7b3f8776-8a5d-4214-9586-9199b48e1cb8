# 📋 CNSS API Descriptions et Exemples

## Vue d'ensemble

Ce document présente une description détaillée de chaque API CNSS avec des exemples d'utilisation dans le contexte de WinMed.

---

## 🔐 APIs d'Authentification

### FIA1 - Authentification Professionnel de Santé
**Objectif**: Authentifier un médecin avec ses identifiants CNSS (INPE)

**Exemple d'utilisation WinMed**:
```json
{
  "Login": "12345678",
  "Password": "motdepasse_cnss"
}
```

**Réponse attendue**:
```json
{
  "CodeRetour": "FP2-001",
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### FIA2 - Exchange Token Éditeur
**Objectif**: Échanger le token PS contre un token éditeur pour WinMed

**Exemple d'utilisation WinMed**:
```json
{
  "Client_id": "winmed_client_id",
  "client_secret": "winmed_secret_key",
  "Access_Token": "token_from_FIA1"
}
```

---

## 👤 APIs de Gestion des Assurés

### FIP1 - Signalétique Assuré
**Objectif**: Récupérer les informations d'un patient CNSS

**Exemple d'utilisation WinMed**:
```json
{
  "NumeroImmatriculataion": "123456789",
  "CNIE_CS": "AB123456",
  "Date_naissance": "1985-03-15"
}
```

**Réponse attendue**:
```json
{
  "CodeRetour": "FIP1-001",
  "listeBeneficiaires": [
    {
      "numeroImmatriculation": "123456789",
      "Nom": "ALAMI",
      "Prenom": "Ahmed",
      "dateNaissance": "1985-03-15",
      "Genre": "H",
      "NumIndividu": "001",
      "LienParente": "ASSURE"
    }
  ]
}
```

**Intégration WinMed**: Cette API sera appelée lors de la création/recherche d'un patient pour vérifier son statut CNSS et récupérer ses informations.

---

## 📋 APIs de Gestion FSE (Feuille de Soins Électronique)

### FIP2 - Vérification FSE
**Objectif**: Valider une FSE avant soumission officielle

**Exemple d'utilisation WinMed**:
```json
{
  "NumeroImmatriculataion": "123456789",
  "NumeroIndividu": "001",
  "INPE_Medecin": "87654321",
  "DateVisite": "2024-01-15",
  "CodePathologie": "Z00.0",
  "Description": "Consultation de routine",
  "actes": [
    {
      "Code": "C001",
      "Libelle": "Consultation généraliste",
      "CategorieActe": "CONSULTATION",
      "prixUnitiaire": 200.00,
      "DateRealisataion": "2024-01-15",
      "NombreActes": 1
    }
  ],
  "medicaments": [
    {
      "Code": "MED001",
      "Libelle": "Paracétamol 500mg",
      "Dosage": "500",
      "UniteDosage": "mg",
      "UniteParjour": 3,
      "NombreJour": 7
    }
  ]
}
```

### FIP3 - Déclaration FSE
**Objectif**: Soumettre officiellement une FSE à la CNSS

**Exemple d'utilisation WinMed**:
```json
{
  "NumeroImmatriculataion": "123456789",
  "NumeroIndividu": "001",
  "INPE_Medecin": "87654321",
  "DateVisite": "2024-01-15",
  "CodePathologie": "Z00.0",
  "Description": "Consultation de routine",
  "actes_realises": [
    {
      "Code": "C001",
      "Libelle": "Consultation généraliste",
      "CategorieActe": "CONSULTATION",
      "prixUnitiaire": 200.00,
      "DateRealisataion": "2024-01-15",
      "NombreActes": 1
    }
  ],
  "actes_adresses": [
    {
      "Code": "R001",
      "Libelle": "Radiographie thorax",
      "CategorieActe": "RADIOLOGIE",
      "NombreActes": 1
    }
  ],
  "medicaments": [
    {
      "Code": "MED001",
      "Libelle": "Paracétamol 500mg",
      "Dosage": "500",
      "UniteDosage": "mg",
      "UniteParjour": 3,
      "NombreJour": 7
    }
  ]
}
```

**Réponse attendue**:
```json
{
  "CodeRetour": "FIP3-001",
  "MessageRetour": "FSE créée avec succès",
  "NumeroFSE": "FSE2024001234"
}
```

### FIP4 - Recherche FSE
**Objectif**: Récupérer les détails d'une FSE existante

**Exemple d'utilisation WinMed**:
```json
{
  "NumeroFSE": "FSE2024001234"
}
```

---

## 🔄 APIs de Modification

### FIP5 - Modification Prescription Pharmacie
**Objectif**: Modifier une prescription médicamenteuse dans une FSE

**Exemple d'utilisation WinMed**:
```json
{
  "NumeroFSE": "FSE2024001234",
  "IdPrescriptionPharmacie": "PRESC001",
  "Code": "MED002",
  "Libelle": "Amoxicilline 500mg",
  "Dosage": "500",
  "UniteDosage": "mg",
  "UniteParjour": 2,
  "NombreJour": 10
}
```

### FIP6 - Modification Prescription Acte
**Objectif**: Modifier un acte prescrit dans une FSE

**Exemple d'utilisation WinMed**:
```json
{
  "NumeroFSE": "FSE2024001234",
  "IdPrescriptionActe": "ACTE001",
  "Code": "R002",
  "Libelle": "Échographie abdominale",
  "CategorieActe": "RADIOLOGIE",
  "NombreActes": 1,
  "IsEP": false
}
```

---

## 📝 APIs de Compléments

### FIC1 - Liste Compléments
**Objectif**: Récupérer les demandes de compléments en attente

**Réponse attendue**:
```json
{
  "CodeRetour": "FIP7-001",
  "ListeComplments": [
    {
      "NumeroFSE": "FSE2024001234",
      "ComplementId": "COMP001",
      "Complément": "Justificatif médical requis pour l'acte prescrit"
    }
  ]
}
```

### FIC2 - Envoi Compléments
**Objectif**: Répondre aux demandes de compléments

**Exemple d'utilisation WinMed**:
```json
{
  "NumeroFSE": "FSE2024001234",
  "ComplmentID": "COMP001",
  "ComplmentRetour": "Justificatif médical en pièce jointe",
  "PJ": ["document_medical.pdf"]
}
```

---

## 📚 APIs de Référentiels

### FIR1 - Référentiel Médicaments
**Objectif**: Synchroniser la liste des médicaments CNSS

**Réponse attendue**:
```json
{
  "CodeRetour": "FIR1-001",
  "medicaments": [
    {
      "Code": "MED001",
      "Libelle": "Paracétamol 500mg",
      "Dosage": "500mg",
      "Forme": "Comprimé",
      "UnitesParBoite": 20
    }
  ]
}
```

### FIR2 - Référentiel Dispositifs Médicaux
**Objectif**: Synchroniser la liste des dispositifs médicaux CNSS

### FIR3 - Référentiel Actes Médicaux
**Objectif**: Synchroniser la liste des actes médicaux CNSS

### FIR4 - Référentiel Actes Biologiques
**Objectif**: Synchroniser la liste des actes de biologie CNSS

### FIR5 - Référentiel ALD/ALC
**Objectif**: Synchroniser la liste des Affections Longue Durée/Coûteuses

---

## 🎯 Cas d'Usage Typiques dans WinMed

### Scénario 1: Nouvelle Consultation
1. Patient arrive → Vérifier avec **FIP1** (Signalétique)
2. Consultation terminée → Valider avec **FIP2** (Vérification FSE)
3. Médecin confirme → Soumettre avec **FIP3** (Déclaration FSE)

### Scénario 2: Modification Post-Consultation
1. Erreur détectée → Utiliser **FIP5** ou **FIP6** pour corriger
2. Complément demandé → Répondre avec **FIC2**

### Scénario 3: Synchronisation Référentiels
1. Mise à jour quotidienne → **FIR1**, **FIR2**, **FIR3**, **FIR4**, **FIR5**
2. Validation des prescriptions contre référentiels CNSS
