{"general": {"appointment": "Appointment", "appointmentTime": "Appointment", "waitingTimes": "Waiting times", "search": "Search", "searchPlaceHolder": "Search by First name, Last name ...", "minuteShort": "min", "minute": "minute", "minutes": "minutes", "type": "Type", "email": "Email", "years": "y.o", "days": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "from": "From", "to": "to", "choseDate": "Choose a date", "files": "Files", "warningTitle": "Warning", "addButton": "ADD", "modifyButton": "MODIFY", "selectProfile": "Select a", "patients": "patients", "sex": {"male": "Male", "female": "Female"}}, "login": {"password": "Password", "logIn": "LogIn", "reset": "Reset", "emailOrPasswordError": "The email or password is not correct", "emailError": "The email is not valid", "resetMessage": "A password reset link has been sent to your email", "passwordForgotten": "Forgot your password ?", "returnToLogin": "return to login"}, "navbar": {"noNotifications": "No notifications", "myAccount": "My account", "disconnect": "Logout", "averageWaitingTime": "Average waiting time", "coming": "To come", "waiting": "Waiting", "done": "Passed", "averageSessionTime": "Average session time"}, "pages": {"home": "Home", "appointments": "Appointments", "currentSession": "CurrentSession", "patients": "Patients", "provisions": "Provisions", "diagnosis": "Diagnoses", "stats": "Statistics", "configuration": "Configuration", "termsOfUse": "Terms of Use"}, "profileTypes": {"patient": "patient", "doctor": "doctor", "receptionist": "receptionist", "supplier": "supplier"}, "doctorSummary": {"nextAppointment": "next Appointment", "noAppointmentsInTheWaitingList": "NO PENDING APPOINTMENTS", "appointment": "Appointment", "ofLateness": "late", "noDescriptionFound": "No description", "allergies": "Allergies", "chronicDiseases": "Chronic diseases", "permanentDrugs": "permanent drugs", "appointmentFiles": "Appointment Files", "noResults": "No results", "noSessionFound": "NO SESSION HAS BEEN AFFECTED", "peopleWaiting": "People waiting", "averageWaitingTime": "Average waiting time", "validate": "Validate", "futureAppointments": "Upcoming appointments", "noAppointmentsItemsInTheWaitingList": "NO APPOINTMENT ON HOLD", "goToAppointments": "goToAppointments", "breaksAndUnavailability": "Breaks and unavailability", "sessionsHistory": "SESSION HISTORY", "viewMore": "See more"}, "doctorSelection": {"doctor": "Doctor", "selectAll": "Select all"}, "breaks": {"atClinic": "At the clinic", "atPatient": "At patient", "break": "Pause", "dailyBreak": "Lunch break", "start": "Start", "end": "End", "timeLeft": "Remaining time", "status": "Status", "disabled": "Disabled", "alreadyPassed": "Already passed"}, "appointments": {"search": "Search", "passedSessions": "Sessions processed", "date": "Date", "from": "From", "to": "To", "noBreaksFound": "Aucun indisponibilité n'est trouvée", "noAppointmentsInHold": "AUCUN RENDEZ-VOUS EN COURS", "noAppointmentsFound": "AUCUN RENDEZ-VOUS EN ATTENTE", "states": {"inProgress": "En cours", "waiting": "En attente", "canceled": "<PERSON><PERSON><PERSON>", "passed": "Traitée", "almostCompleted": "Vient de finir"}, "options": {"edit": "Modifier le rendez-vous", "sendMessage": "Envoyer un message", "downloadInvoice": "Télécharger la facture", "downloadPrescription": "Télécharger l'ordonnance", "printInvoice": "Imprimer la facture", "printPrescription": "Imprimer l'ordonnance"}, "appointmentsConflict": "il y a un conflit entre rendez-vous", "dialog": {"propositions": "Propositions", "fullDay": "<PERSON><PERSON><PERSON> pleine", "noDoctorSelectedInfo": "Sélectionner un médecin pour obtenir les disponibilités", "visitType": "Type de visite", "comment": "Commentaire", "commentPlaceHolder": "Ex. le rendez-vous ...", "requiredDocuments": "Documents to provide at the appointment", "requiredDocumentsPlaceHolder": "Nouveau document..."}}, "currentSession": {"noAffectedSession": "AUCUNE SESSION N'A ÉTÉ AFFECTÉE", "endSession": "Mettre fin", "scheduleFutureAppointment": "Programmer un futur rendez-vous", "notes": "NOTES", "note": "NOTE", "prescription": "ORDONNANCE", "diagnoses": "DIAGNOSTICS", "diagnose": "Diagnostic", "notesList": {"addA": "Ajouter une", "withoutTitle": "sans titre", "note": "note", "noteTitle": "Titre de note", "descriptionDefault": "Note"}, "prescriptionList": {"addA": "Ajouter un", "withoutTitle": "sans titre", "drug": "médicament", "usage": "utilisation", "prescriptionTitle": "Titre de médicament", "descriptionDefault": "Conseil d'utilisation"}}, "patients": {"noPatientsFound": "AUCUN PATIENT TROUVÉ", "appointmentCount": "Total des rendez-vous", "phoneNumber": "Numéro de téléphone", "appointments": "rendez-vous"}, "supplies": {"noSuppliesFound": "AUCUN PATIENT TROUVÉ", "quantityAvailable": "Quantité en stock", "item": "article", "usedItem": "consommation", "averageTime": "<PERSON><PERSON><PERSON> moyenne", "consumption": "Consommations", "sellingPrice": "Prix de vente", "itemType": "Nature d'article", "types": {"material": "<PERSON><PERSON><PERSON>", "drug": "Médicament", "session": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>", "radiologie": "Radiologie", "biologie": "Biologie"}, "dialog": {"anItem": "UN ARTICLE", "name": "Nom", "namePlaceholder": "Article", "type": "Type", "quantity": "Quantité", "buyingPrice": "Prix coûtant", "sellingPrice": "Prix de vente", "description": "Description", "descriptionPlaceholder": "Cette maladie est.."}}, "diagnoses": {"noDiagnosesFound": "AUCUN DIAGNOSTIC TROUVÉ", "specialty": "Specialité", "visitsCount": "Nombre de visites", "visit": "visite", "patientsCount": "Nombre de patients", "patient": "patient", "severity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dangerTypes": {"severe": "Grave", "moderate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minor": "Mineure", "unknown": "Inconnue"}, "dialog": {"aDiagnose": "UN DIAGNOSE", "name": "Nom", "namePlaceholder": "Diagnosis", "specialty": "Specialité", "description": "Description", "descriptionPlaceholder": "Cette maladie est..", "severity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contagious": "Contag<PERSON><PERSON>"}}, "stats": {"week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON>", "fromTheBeginning": "Dès le début", "appointmentsCount": "Nombre totale des rendez-vous", "effectivenessRate": "<PERSON><PERSON> d'efficaci<PERSON>", "totalGain": "Bénéfices totale", "averageWaitingTime": "Temps d'attente moyen", "appointmentDistributionByType": "Distribution des rendez-vous par types", "appointmentDistributionByGain": "Distribution des bénéfices par types", "appointmentsAveragesByWeeks": "moyenne de rendez-vous par jours de semaine", "averageWaitingTimesVariation": "Variation de temps d'attente moyen/temps de retard", "evolution": "Evolution du taux d'efficacité,heures d'ouverture et heures d'occupation", "appointmentsCountEvolution": "Evolution de nombre des rendez-vous par types dans le temps", "gainEvolution": "Evolution des bénéfices par types  dans le temps"}, "config": {"office": "Cabinet", "breaks": "Pauses et indisponibilité", "team": "Equipe", "generalInfo": {"infoAboutTheOffice": "Information et contact du cabinet", "officeName": "Nom de cabinet", "officeType": "Type de cabinet", "mobile": "Télephone", "phone": "Télephone fixe", "fax": "Fax", "email": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "averageSessionTime": "Durée moyenne des séances", "currency": "<PERSON><PERSON><PERSON><PERSON>", "hospitalTypes": {"medicalOffice": "Cabinet Medicale", "dentalOffice": "Cabinet <PERSON>taire", "clinic": "Clinique", "careFacility": "Etablissements de soin", "other": "<PERSON><PERSON>"}, "sessionTypes": "Types des sessions", "sessionType": {"defaultName": "<PERSON><PERSON><PERSON>", "price": "Prix par defaut", "averageTime": "<PERSON><PERSON><PERSON> moyenne"}, "save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "schedule": "<PERSON><PERSON><PERSON>", "defaultSchedule": "<PERSON><PERSON> <PERSON><PERSON>", "workTime": "Heures de travail", "breakTime": "<PERSON><PERSON> de pauses", "to": "À", "specialDays": "JOURS SPECIALS"}, "fixBreaks": {"noBreaksFound": "AUCUN PAUSE OU INDISPONIBILITÉ N'EST DECLARÉ"}}, "timeOffsDialogue": {"choseDate": "Choisir une date", "choseWeekDay": "<PERSON><PERSON> un jour de semaine", "conflict": "il y a un conflit avec les rendez-vous", "comment": "Commentaire"}, "search": {"searchProfile": "RECHERCHER UN PROFILE", "searchListOf": "Rechercher la liste des", "noResults": "AUCUN PROFILE TROUVÉ"}, "profileDialog": {"lastName": "Nom", "firstName": "Prénom", "profileType": "Type de profile", "sex": "<PERSON>e", "chooseSex": "choisissez le sexe", "phoneNumber": "Numéro de téléphone", "email": "Email", "cardID": "CIN", "birthDate": "Date de naissance", "insurance": "Assurance", "insuranceID": "Idantifiant d'assurance", "address": "Addresse", "showDetails": "Informations supplémentaires", "isAdmin": "Est Manager ?", "professionalInformation": "Informations professionnelles", "identifier": "Identifiant", "title": "Titre", "chooseTitle": "choisissez la profession", "titleOptions": {"admin": "Administrateur", "doctor": "<PERSON><PERSON><PERSON>", "nurse": "<PERSON><PERSON>rm<PERSON>"}, "position": "Position", "choosePosition": "choisissez la position", "chooseProfession": "choisissez la profession d'abord", "residency": "Résidence", "chooseResidency": "choisissez la résidence", "seniority": "Ancienneté", "chooseSeniority": "choisissez la seniorité", "nursePositionOptions": {"ibode": "IBODE", "ide": "IDE", "iade": "IADE"}, "DoctorPositionOptions": {"surgeon": "<PERSON><PERSON><PERSON><PERSON>", "physician": "Médecin", "anesthetist": "Anesthésiste"}, "residencyOptions": {"titular": "Titulaire", "temporary": "<PERSON><PERSON><PERSON>"}, "seniorityOptions": {"senior": "Senior", "junior": "Junior", "intern": "Intern"}}, "account": {"profile": {"title": "Profile", "description": "Informations génerales du profile", "editProfile": "Modifier les infos de profile", "editProfileDescriptionP1": "remplissez le formulaire et appuyez sur", "editProfileDescriptionP2": "pour enregistrer les modifications", "validateButton": "Valider", "addProfile": "Ajouter un profile", "addButton": "Ajouter", "personalInformation": "Informations personnels"}, "security": {"title": "Sécurité du compte", "description": "Sécurité et connexion", "changePassword": "Modifier votre mot de passe", "editSecurityDescriptionP1": "remplissez le formulaire et appuyez sur", "editSecurityDescriptionP2": "pour enregistrer les modifications", "validateButton": "Valider", "oldPassword": "Entrez votre ancien mot de passe", "oldPasswordError": "<PERSON><PERSON> de<PERSON> entrer le mot de passe actuel", "newPassword": "Entrez votre nouveau mot de passe", "newPasswordError": "V<PERSON> devez entrer un nouveau mot de passe", "newPasswordConfirmation": "V<PERSON> devez entrer un nouveau mot de passe", "newPasswordConfirmationError": "<PERSON><PERSON> de<PERSON> confirmer le nouveau mot de passe", "passwordsNotTheSame": "les mots de passes ne sont pas identiques"}}, "invoice": {"invoice": "Invoice", "patient": "PATIENT", "adress": "<PERSON>ress", "email": "EMAIL", "mr": "Mr.", "type": "TYPE", "doctor": "DOCTOR", "date": "DATE", "name": "Designation", "price": "PRICE", "quantity": "QUANTITY", "total": "TOTAL", "finalTotal": "Total", "downloadPdf": "Download PDF", "endSession": "End", "endSession2": "close the appointment", "details": "details"}, "prescription": {"prescription": "PRESCRIPTION", "patient": "PATIENT", "adress": "<PERSON>ress", "email": "EMAIL", "doctor": "DOCTOR", "date": "DATE", "drugs": "MEDICATIONS"}, "currency": {"mad": {"mad": "<PERSON><PERSON><PERSON> maro<PERSON>in", "madShort": "DH"}, "sad": {"sad": "<PERSON><PERSON><PERSON>", "sadShort": "SAR"}, "egp": {"egp": "Egyptian pound", "egpShort": "EGP"}}, "termsOfUse": {"title": "Terms of Use", "subtitle": "WinMed - Medical Management Application", "section1": {"title": "1. Application Overview", "content": "WinMed is a comprehensive medical management application designed to streamline medical practice operations, patient record management, and CNSS (National Social Security Fund) integration for healthcare professionals."}, "section2": {"title": "2. Features and Services", "content": "Our application provides the following key features:", "feature1": "Complete patient medical record management", "feature2": "Appointment scheduling and management system", "feature3": "CNSS integration for insurance processing", "feature4": "Medical billing and invoice generation"}, "section3": {"title": "3. Free Trial Period", "content": "WinMed is available free of charge until December 31, 2025. After this date, subscription fees will apply based on the selected service plan."}, "section4": {"title": "4. Support Services", "content": "Technical support is available as a paid service. Support includes troubleshooting, training, and technical assistance. Support fees are separate from the application subscription."}, "section5": {"title": "5. Account Inactivity Policy", "content": "For users on the free plan: If your account remains inactive for more than 2 consecutive months, your account and all associated data will be permanently deleted from our servers."}, "section6": {"title": "6. Data Responsibility", "content": "Users are responsible for maintaining regular backups of their data. We recommend exporting important data regularly to ensure data security and continuity."}, "section7": {"title": "7. User Obligations", "content": "Users must comply with all applicable medical privacy laws and regulations when using WinMed. The application must be used in accordance with professional medical standards."}, "section8": {"title": "8. Service Availability", "content": "While we strive to maintain 99% uptime, we cannot guarantee uninterrupted service. Scheduled maintenance will be announced in advance when possible."}, "section9": {"title": "9. Important Notice", "content": "Please ensure regular use of your account and data backup to avoid data loss due to inactivity policies."}, "section10": {"title": "10. Contact Information", "content": "For questions about these terms or technical support, please contact our support team through the application or visit our website."}, "lastUpdated": "Last updated: January 2025", "contactInfo": "For support inquiries, please contact: <EMAIL>"}, "pagination": {"showing": "Ligne", "page": "Page", "of": "De", "items": "Ligne(s)", "next": "Suivant", "previous": "Précedent", "loadMore": "Afficher Plus"}}