# 🗄️ Modifications Base de Données pour CNSS

## Vue d'ensemble

Ce document détaille les modifications nécessaires aux modèles MongoDB existants et les nouveaux modèles à créer pour la compatibilité CNSS 100%.

---

## 📊 Dictionnaire des Attributs CNSS ↔ WinMed

### Mapping Patient/Profile
| Attribut CNSS | Attribut WinMed | Type | Obligatoire | Nouveau |
|---------------|-----------------|------|-------------|---------|
| numeroImmatriculation | cnssNumber | String | Oui | ✅ |
| NumIndividu | cnssIndividuNumber | String | Oui | ✅ |
| CNIE_CS | cnieNumber | String | Non | ✅ |
| Nom | lastName | String | Oui | ✅ Existant |
| Prenom | firstName | String | Oui | ✅ Existant |
| dateNaissance | birthDate | Date | Oui | ✅ Existant |
| Genre | gender | String | Oui | ✅ Existant |
| LienParente | familyRelation | String | Non | ✅ |

### Mapping Médecin/Staff
| Attribut CNSS | Attribut WinMed | Type | Obligatoire | Nouveau |
|---------------|-----------------|------|-------------|---------|
| INPE_Medecin | inpeNumber | String | Oui | ✅ |
| INPE_Etablissement | hospitalINPE | String | Non | ✅ |

### Mapping FSE
| Attribut CNSS | Attribut WinMed | Type | Obligatoire | Nouveau |
|---------------|-----------------|------|-------------|---------|
| NumeroFSE | cnssNumber | String | Non | ✅ |
| DateVisite | visitDate | Date | Oui | ✅ |
| CodePathologie | cim11Code | String | Non | ✅ |
| Description | pathologyDescription | String | Non | ✅ |

---

## 🔄 Modifications des Modèles Existants

### 1. Patient.js - Ajouts CNSS
```javascript
// Nouveaux champs à ajouter
const PatientSchema = new mongoose.Schema({
    // ... champs existants ...
    
    // === NOUVEAUX CHAMPS CNSS ===
    cnssNumber: {
        type: String,
        unique: true,
        sparse: true,
        index: true
    },
    cnssIndividuNumber: {
        type: String
    },
    cnieNumber: {
        type: String,
        index: true
    },
    familyRelation: {
        type: String,
        enum: ['ASSURE', 'CONJOINT', 'ENFANT', 'ASCENDANT'],
        default: 'ASSURE'
    },
    cnssEligible: {
        type: Boolean,
        default: false
    },
    cnssVerificationDate: {
        type: Date
    },
    cnssLastSync: {
        type: Date
    },
    // Référence vers l'assuré principal si ayant droit
    principalInsured: {
        type: Schema.Types.ObjectId,
        ref: 'Patient'
    }
});
```

### 2. Staff.js - Ajouts CNSS
```javascript
// Nouveaux champs à ajouter
const StaffSchema = new mongoose.Schema({
    // ... champs existants ...
    
    // === NOUVEAUX CHAMPS CNSS ===
    inpeNumber: {
        type: String,
        unique: true,
        sparse: true,
        index: true
    },
    cnssVerified: {
        type: Boolean,
        default: false
    },
    cnssVerificationDate: {
        type: Date
    },
    cnssTokens: [{
        token: String,
        refreshToken: String,
        expiresAt: Date,
        createdAt: { type: Date, default: Date.now }
    }]
});
```

### 3. Hospital.js - Ajouts CNSS
```javascript
// Nouveaux champs à ajouter
const HospitalSchema = new mongoose.Schema({
    // ... champs existants ...
    
    // === NOUVEAUX CHAMPS CNSS ===
    inpeNumber: {
        type: String,
        unique: true,
        sparse: true
    },
    cnssClientId: {
        type: String
    },
    cnssClientSecret: {
        type: String
    },
    cnssEnabled: {
        type: Boolean,
        default: false
    },
    cnssLastSync: {
        type: Date
    }
});
```

### 4. Session.js - Ajouts CNSS
```javascript
// Nouveaux champs à ajouter
const SessionSchema = new mongoose.Schema({
    // ... champs existants ...
    
    // === NOUVEAUX CHAMPS CNSS ===
    fse: {
        type: Schema.Types.ObjectId,
        ref: 'FSE'
    },
    cnssEligible: {
        type: Boolean,
        default: false
    },
    cim11Code: {
        type: String
    },
    pathologyDescription: {
        type: String
    }
});
```

### 5. Drug.js - Ajouts CNSS
```javascript
// Nouveaux champs à ajouter
const DrugSchema = new mongoose.Schema({
    // ... champs existants ...
    
    // === NOUVEAUX CHAMPS CNSS ===
    cnssCode: {
        type: String,
        index: true
    },
    cnssLibelle: {
        type: String
    },
    cnssDosage: {
        type: String
    },
    cnssForme: {
        type: String
    },
    cnssUnitsPerBox: {
        type: Number
    },
    cnssReimbursable: {
        type: Boolean,
        default: false
    },
    cnssLastSync: {
        type: Date
    }
});
```

### 6. Radiograph.js - Ajouts CNSS
```javascript
// Nouveaux champs à ajouter
const RadiographSchema = new mongoose.Schema({
    // ... champs existants ...
    
    // === NOUVEAUX CHAMPS CNSS ===
    cnssCode: {
        type: String,
        index: true
    },
    cnssLibelle: {
        type: String
    },
    cnssCotation: {
        type: String
    },
    cnssRequiresEP: {
        type: Boolean,
        default: false
    },
    cnssCategory: {
        type: String,
        enum: ['RADIOLOGIE', 'ECHOGRAPHIE', 'SCANNER', 'IRM']
    },
    cnssLastSync: {
        type: Date
    }
});
```

### 7. Biologie.js - Ajouts CNSS
```javascript
// Nouveaux champs à ajouter
const BiologieSchema = new mongoose.Schema({
    // ... champs existants ...
    
    // === NOUVEAUX CHAMPS CNSS ===
    cnssCode: {
        type: String,
        index: true
    },
    cnssLibelle: {
        type: String
    },
    cnssCotation: {
        type: String
    },
    cnssRequiresEP: {
        type: Boolean,
        default: false
    },
    cnssLastSync: {
        type: Date
    }
});
```

---

## 🆕 Nouveaux Modèles à Créer

### 1. FSE.js - Feuille de Soins Électronique
```javascript
import mongoose from "mongoose";
const Schema = mongoose.Schema;

const FSESchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital',
        required: true
    },
    session: {
        type: Schema.Types.ObjectId,
        ref: 'Session',
        required: true
    },
    patient: {
        type: Schema.Types.ObjectId,
        ref: 'Patient',
        required: true
    },
    doctor: {
        type: Schema.Types.ObjectId,
        ref: 'Staff',
        required: true
    },
    
    // Identifiants CNSS
    cnssNumber: {
        type: String,
        unique: true,
        sparse: true
    },
    
    // Données médicales
    visitDate: {
        type: Date,
        required: true
    },
    cim11Code: {
        type: String
    },
    pathologyDescription: {
        type: String
    },
    
    // Actes réalisés
    performedActs: [{
        cnssCode: String,
        libelle: String,
        localisation: String,
        quantity: Number,
        category: String,
        unitPrice: Number,
        realizationDate: Date,
        requiresEP: Boolean,
        executed: { type: Boolean, default: false }
    }],
    
    // Actes adressés (prescriptions)
    prescribedActs: [{
        cnssCode: String,
        libelle: String,
        quantity: Number,
        category: String,
        requiresEP: Boolean,
        executed: { type: Boolean, default: false }
    }],
    
    // Médicaments
    medications: [{
        cnssCode: String,
        libelle: String,
        dosage: String,
        forme: String,
        uniteDosage: String,
        unitsPerDay: Number,
        treatmentDays: Number,
        dispensed: { type: Boolean, default: false }
    }],
    
    // Dispositifs médicaux
    medicalDevices: [{
        cnssCode: String,
        libelle: String,
        quantity: Number,
        executed: { type: Boolean, default: false }
    }],
    
    // Statut FSE
    status: {
        type: String,
        enum: ['DRAFT', 'VERIFIED', 'SUBMITTED', 'ACCEPTED', 'REJECTED', 'MODIFIED'],
        default: 'DRAFT'
    },
    
    // Vérification CNSS
    cnssVerification: {
        verified: { type: Boolean, default: false },
        verificationDate: Date,
        alerts: [{
            code: String,
            message: String,
            type: { type: String, enum: ['WARNING', 'ERROR', 'INFO'] }
        }],
        errors: [String]
    },
    
    // Soumission CNSS
    cnssSubmission: {
        submitted: { type: Boolean, default: false },
        submissionDate: Date,
        responseCode: String,
        responseMessage: String
    },
    
    // Compléments
    complements: [{
        complementId: String,
        description: String,
        response: String,
        attachments: [String],
        status: { type: String, enum: ['PENDING', 'RESPONDED'], default: 'PENDING' },
        requestDate: Date,
        responseDate: Date
    }],
    
    // Métadonnées
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    }
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});

FSESchema.index({ cnssNumber: 1 });
FSESchema.index({ patient: 1, visitDate: -1 });
FSESchema.index({ doctor: 1, visitDate: -1 });
FSESchema.index({ status: 1 });

module.exports = mongoose.model("FSE", FSESchema);
```

### 2. CNSSReferential.js - Référentiels CNSS
```javascript
import mongoose from "mongoose";
const Schema = mongoose.Schema;

const CNSSReferentialSchema = new mongoose.Schema({
    type: {
        type: String,
        enum: ['DRUG', 'MEDICAL_DEVICE', 'MEDICAL_ACT', 'BIOLOGY_ACT', 'ALD_ALC'],
        required: true
    },
    
    // Identifiants
    cnssCode: {
        type: String,
        required: true,
        index: true
    },
    libelle: {
        type: String,
        required: true
    },
    
    // Spécifique aux médicaments
    dosage: String,
    forme: String,
    unitsPerBox: Number,
    
    // Spécifique aux actes et dispositifs
    cotation: String,
    requiresEP: Boolean,
    category: String,
    units: Number,
    
    // Spécifique aux ALD/ALC
    exonerationRate: Number,
    
    // Métadonnées
    active: {
        type: Boolean,
        default: true
    },
    lastSyncDate: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true
});

CNSSReferentialSchema.index({ type: 1, cnssCode: 1 }, { unique: true });
CNSSReferentialSchema.index({ type: 1, active: 1 });

module.exports = mongoose.model("CNSSReferential", CNSSReferentialSchema);
```

### 3. CNSSToken.js - Gestion des Tokens CNSS
```javascript
import mongoose from "mongoose";
const Schema = mongoose.Schema;

const CNSSTokenSchema = new mongoose.Schema({
    staff: {
        type: Schema.Types.ObjectId,
        ref: 'Staff',
        required: true
    },
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital',
        required: true
    },
    
    // Tokens
    accessToken: {
        type: String,
        required: true
    },
    refreshToken: String,
    
    // Métadonnées
    expiresAt: {
        type: Date,
        required: true
    },
    tokenType: {
        type: String,
        default: 'Bearer'
    },
    scope: String,
    
    // Statut
    active: {
        type: Boolean,
        default: true
    },
    revokedAt: Date,
    
    // Utilisation
    lastUsed: Date,
    usageCount: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true
});

CNSSTokenSchema.index({ staff: 1, active: 1 });
CNSSTokenSchema.index({ expiresAt: 1 });

module.exports = mongoose.model("CNSSToken", CNSSTokenSchema);
```

---

## 🔧 Scripts de Migration

### Migration Script - add_cnss_fields.js
```javascript
// Script pour ajouter les nouveaux champs CNSS aux collections existantes
import mongoose from 'mongoose';

const migrationScript = async () => {
    // Ajouter champs CNSS aux patients existants
    await mongoose.connection.db.collection('patients').updateMany(
        {},
        {
            $set: {
                cnssEligible: false,
                familyRelation: 'ASSURE'
            }
        }
    );
    
    // Ajouter champs CNSS aux staff existants
    await mongoose.connection.db.collection('staffs').updateMany(
        {},
        {
            $set: {
                cnssVerified: false,
                cnssTokens: []
            }
        }
    );
    
    // Ajouter champs CNSS aux hôpitaux existants
    await mongoose.connection.db.collection('hospitals').updateMany(
        {},
        {
            $set: {
                cnssEnabled: false
            }
        }
    );
    
    console.log('Migration CNSS terminée');
};
```

---

## 📋 Index Recommandés

### Nouveaux Index à Créer
```javascript
// Patient
db.patients.createIndex({ "cnssNumber": 1 }, { unique: true, sparse: true });
db.patients.createIndex({ "cnieNumber": 1 });

// Staff
db.staffs.createIndex({ "inpeNumber": 1 }, { unique: true, sparse: true });

// FSE
db.fses.createIndex({ "cnssNumber": 1 }, { unique: true, sparse: true });
db.fses.createIndex({ "patient": 1, "visitDate": -1 });
db.fses.createIndex({ "status": 1 });

// CNSSReferential
db.cnssreferentials.createIndex({ "type": 1, "cnssCode": 1 }, { unique: true });
db.cnssreferentials.createIndex({ "type": 1, "active": 1 });

// CNSSToken
db.cnsstokens.createIndex({ "staff": 1, "active": 1 });
db.cnsstokens.createIndex({ "expiresAt": 1 });
```
